package adhoc.helper

import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import org.springframework.boot.SpringApplication
import org.springframework.context.ConfigurableApplicationContext
import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*

class AdhocHelper {

	private static localStackPort

	private static DockerComposeContainer composeContainer
	public static final String LOCAL_STACK_PORT = "4566"
	public static final String LOCALSTACK = "localstack"

	static String getLocalStackPort() {
		return localStackPort
	}

	static {
		startContainer()
	}

	private static void startContainer() {
		try {
			composeContainer = new DockerComposeContainer(new File("docker-compose-test.yml"))
					.withExposedService(LOCALSTACK, 4566)
					.waitingFor(LOCALSTACK, Wait.forListeningPort())

			composeContainer.start()
			// Get the actual mapped port from the container
			localStackPort = composeContainer.getServicePort(LOCALSTACK, 4566).toString()
			println("LocalStack started successfully on port: " + localStackPort)
		} catch (Exception e) {
			println("Failed to start LocalStack container: " + e.getMessage())
			e.printStackTrace()
			// Fallback to default port if container fails to start
			localStackPort = LOCAL_STACK_PORT
		}
	}

	static void cleanupSpec() {
		//To do cleanup Spec
	}

	static boolean tableExists(DynamoDbClient dynamoDbClient, String tableName) {
		println("Checking if table exists: " + tableName)
		DescribeTableRequest request = DescribeTableRequest.builder()
				.tableName(tableName)
				.build()

		try {
			def result = dynamoDbClient.describeTable(request)
			println("Table exists: " + tableName)
			return true
		} catch (ResourceNotFoundException ex) {
			println("Table does not exist: " + tableName)
			return false
		} catch (Exception e) {
			println("Error checking table existence: " + e.getMessage())
			e.printStackTrace()
			return false
		}
	}

	static void createEventsTable(DynamoDbClient dynamoDbClient, String tableName) {
		if (!tableExists(dynamoDbClient, tableName)) {
			CreateTableRequest createTableRequest = CreateTableRequest.builder()
					.tableName(tableName)
					.keySchema(
					KeySchemaElement.builder().attributeName("transactionHash").keyType(KeyType.HASH).build(),
					KeySchemaElement.builder().attributeName("logIndex").keyType(KeyType.RANGE).build()
					)
					.attributeDefinitions(
					AttributeDefinition.builder().attributeName("transactionHash").attributeType(ScalarAttributeType.S).build(),
					AttributeDefinition.builder().attributeName("logIndex").attributeType(ScalarAttributeType.N).build()
					)
					.provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
					.build() as CreateTableRequest

			dynamoDbClient.createTable(createTableRequest)
		}
	}

	static void createBlockHeightTable(DynamoDbClient dynamoDbClient, String tableName) {
		println("Attempting to create table: " + tableName)
		if (!tableExists(dynamoDbClient, tableName)) {
			println("Table does not exist, creating: " + tableName)
			CreateTableRequest createTableRequest = CreateTableRequest.builder()
					.tableName(tableName)
					.keySchema(
					KeySchemaElement.builder().attributeName("id").keyType(KeyType.HASH).build()
					)
					.attributeDefinitions(
					AttributeDefinition.builder().attributeName("id").attributeType(ScalarAttributeType.N).build()
					)
					.provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
					.build()

			try {
				def result = dynamoDbClient.createTable(createTableRequest)
				println("Table created successfully: " + tableName)
			} catch (Exception e) {
				println("Error creating table: " + e.getMessage())
				e.printStackTrace()
			}
		} else {
			println("Table already exists: " + tableName)
		}
	}

	static Map<String, AttributeValue> getEventItem(DynamoDbClient dynamoDbClient, String tableName, String transactionHash, int logIndex) {
		GetItemRequest request = GetItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"transactionHash", AttributeValue.builder().s(transactionHash).build(),
				"logIndex", AttributeValue.builder().n(String.valueOf(logIndex)).build()
				))
				.build()

		try {
			GetItemResponse response = dynamoDbClient.getItem(request)
			return response.item()
		} catch (Exception e) {
			return null
		}
	}

	static Map<String, AttributeValue> getBlockHeightItem(DynamoDbClient dynamoDbClient, String tableName, long id) {
		GetItemRequest request = GetItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"id", AttributeValue.builder().n(String.valueOf(id)).build()
				))
				.build()

		try {
			GetItemResponse response = dynamoDbClient.getItem(request)
			return response.item()
		} catch (Exception e) {
			return null
		}
	}

	static boolean saveEventItem(DynamoDbClient dynamoDbClient, String tableName, Map<String, AttributeValue> item) {
		PutItemRequest request = PutItemRequest.builder()
				.tableName(tableName)
				.item(item)
				.build()

		try {
			dynamoDbClient.putItem(request)
			return true
		} catch (Exception e) {
			return false
		}
	}

	static boolean saveBlockHeightItem(DynamoDbClient dynamoDbClient, String tableName, long id, long blockNumber) {
		Map<String, AttributeValue> item = new HashMap<>()
		item.put("id", AttributeValue.builder().n(String.valueOf(id)).build())
		item.put("blockNumber", AttributeValue.builder().n(String.valueOf(blockNumber)).build())

		PutItemRequest request = PutItemRequest.builder()
				.tableName(tableName)
				.item(item)
				.build()

		try {
			dynamoDbClient.putItem(request)
			return true
		} catch (Exception e) {
			return false
		}
	}

	static boolean deleteEventItem(DynamoDbClient dynamoDbClient, String tableName, String transactionHash, int logIndex) {
		DeleteItemRequest request = DeleteItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"transactionHash", AttributeValue.builder().s(transactionHash).build(),
				"logIndex", AttributeValue.builder().n(String.valueOf(logIndex)).build()
				))
				.build()

		try {
			dynamoDbClient.deleteItem(request)
			return true
		} catch (Exception e) {
			return false
		}
	}

	static boolean deleteBlockHeightItem(DynamoDbClient dynamoDbClient, String tableName, long id) {
		DeleteItemRequest request = DeleteItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"id", AttributeValue.builder().n(String.valueOf(id)).build()
				))
				.build()

		try {
			dynamoDbClient.deleteItem(request)
			return true
		} catch (Exception e) {
			return false
		}
	}

	static ConfigurableApplicationContext initApplication() {
		def application = new SpringApplication(BcmonitoringApplication.class)
		return application.run([] as String[])
	}

	static ConfigurableApplicationContext initApplication(Map<String, Object> defaultProperties) {
		def application = new SpringApplication(BcmonitoringApplication.class)
		if (defaultProperties) {
			application.setDefaultProperties(defaultProperties)
		}
		return application.run([] as String[])
	}

	static void closeApplication(ConfigurableApplicationContext applicationContext) {
		if (applicationContext != null) {
			try {
				println("Closing application context")
				applicationContext.close()
				Thread.sleep(1000) // Give time for port to be released
			} catch (Exception e) {
				println("Error closing application context: " + e.message)
			}
		}
	}

	static void resetLocalStack(){
		if (composeContainer != null) {
			try {
				// Method 1a: Standard stop (equivalent to docker-compose down)
				composeContainer.stop()
			} catch (Exception e) {
				println("Error stopping container: " + e.message)
			}
		}
		startContainer()
	}

	static class LogCapture {
		private ByteArrayOutputStream output = new ByteArrayOutputStream()
		private PrintStream originalOut

		void startCapture() {
			originalOut = System.out
			System.setOut(new PrintStream(output))
		}

		List<String> stopCaptureAndGetLogs() {
			System.setOut(originalOut)
			return output.toString().split('\n')
		}

		boolean containsLog(String message) {
			return output.toString().contains(message)
		}
	}
}